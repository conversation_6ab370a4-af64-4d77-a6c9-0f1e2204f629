package thirdparty

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strings"
	"time"
)

var teamviewerMetaData = map[string]interface{}{
	"uuid": "i1j2k3l4-g5h6-7890-fghi-123456789ijk",
	"templateFileNameMap": map[string]interface{}{
		"x64": "TEAMVIEWER_X64.xml",
	},
}

type TeamViewerPoolingService struct {
	ThirdPartyPackageService
}

func (c TeamViewerPoolingService) Name() string {
	return "TeamViewerPoolingService"
}

func init() {
	RegisterCollector(TeamViewerPoolingService{})
}

func NewTeamViewerPoolingService() *TeamViewerPoolingService {
	return &TeamViewerPoolingService{}
}

func (service TeamViewerPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching TeamViewer data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching TeamViewer data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.TEAMVIEWER)
}

func (service TeamViewerPoolingService) fetchLatestPatchData() error {
	// TeamViewer uses a fixed URL that always points to the latest version
	downloadURL := "https://download.teamviewer.com/download/TeamViewer_Setup_x64.exe"

	// Get version from changelog page
	version, err := service.getLatestVersionFromChangelog()
	if err != nil {
		return fmt.Errorf("error getting latest version: %w", err)
	}

	logger.ServiceLogger.Debug("Found TeamViewer version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service TeamViewerPoolingService) getLatestVersionFromChangelog() (string, error) {
	url := "https://community.teamviewer.com/English/categories/change-logs-en"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error making HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %w", err)
	}

	// Parse the changelog to find the latest version
	version, err := service.parseTeamViewerVersion(string(body))
	if err != nil {
		return "", fmt.Errorf("error parsing TeamViewer version: %w", err)
	}

	return version, nil
}

func (service TeamViewerPoolingService) parseTeamViewerVersion(htmlContent string) (string, error) {
	// Look for the first Windows version in the changelog
	// Pattern: [Windows] v15.XX.X
	re := regexp.MustCompile(`\[Windows\]\s+v(\d+\.\d+\.\d+)`)
	matches := re.FindStringSubmatch(htmlContent)
	
	if len(matches) < 2 {
		return "", fmt.Errorf("could not find TeamViewer version in changelog")
	}
	
	version := matches[1]
	return version, nil
}

func (service TeamViewerPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.TEAMVIEWER))
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("Data already exists for TeamViewer version ", version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for TeamViewer ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("TeamViewer_Setup_%s_x64.exe", version)

	// Use current time as release date since TeamViewer doesn't provide this info in the direct download
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.TEAMVIEWER)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.TEAMVIEWER.String(), version, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "TeamViewer",
		},
		Description:      "TeamViewer is a comprehensive remote access, remote control and remote support solution that works with almost every desktop and mobile platform, including Windows, macOS, Android, and iOS.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "TeamViewer Germany GmbH",
		SupportUrl:       "https://www.teamviewer.com/en-us/global/support/",
		ReleaseNote:      "https://community.teamviewer.com/English/categories/change-logs-en",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.TEAMVIEWER,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.TEAMVIEWER)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating TeamViewer package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(teamviewerMetaData, version, osArch, uuid, common.TEAMVIEWER)

	logger.ServiceLogger.Debug("Package data created successfully for TeamViewer version ", version)
	return nil
}
